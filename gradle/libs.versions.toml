[metadata]
format.version = "1.1"

[versions]
awaitility = "4.2.0"
edc = "0.14.0-SNAPSHOT"
restAssured = "5.5.0"
jackson = "2.19.2"
postgres = "42.7.7"
testcontainers = "1.21.3"

[libraries]
awaitility = { module = "org.awaitility:awaitility", version.ref = "awaitility" }
edc-api-management = { module = "org.eclipse.edc:management-api", version.ref = "edc" }
edc-api-version = { module = "org.eclipse.edc:version-api", version.ref = "edc" }
edc-api-control-config = { module = "org.eclipse.edc:control-api-configuration", version.ref = "edc" }
edc-api-observability = { module = "org.eclipse.edc:api-observability", version.ref = "edc" }
edc-boot = { module = "org.eclipse.edc:boot", version.ref = "edc" }
edc-config-filesystem = { module = "org.eclipse.edc:configuration-filesystem", version.ref = "edc" }
edc-core-api = { module = "org.eclipse.edc:api-core", version.ref = "edc" }
edc-core-connector = { module = "org.eclipse.edc:connector-core", version.ref = "edc" }
edc-core-controlplane = { module = "org.eclipse.edc:control-plane-core", version.ref = "edc" }
edc-core-dataPlane-selector = { module = "org.eclipse.edc:data-plane-selector-core", version.ref = "edc" }
edc-core-jersey = { module = "org.eclipse.edc:jersey-core", version.ref = "edc" }
edc-core-jetty = { module = "org.eclipse.edc:jetty-core", version.ref = "edc" }
edc-core-runtime = { module = "org.eclipse.edc:runtime-core", version.ref = "edc" }
edc-core-token = { module = "org.eclipse.edc:token-core", version.ref = "edc" }
edc-core-edrstore = { module = "org.eclipse.edc:edr-store-core", version.ref = "edc" }
edc-dpf-selector-core = { module = "org.eclipse.edc:data-plane-selector-core", version.ref = "edc" }
edc-dpf-selector-spi = { module = "org.eclipse.edc:data-plane-selector-spi", version.ref = "edc" }
edc-ext-http = { module = "org.eclipse.edc:http", version.ref = "edc" }
edc-iam-mock = { module = "org.eclipse.edc:iam-mock", version.ref = "edc" }

edc-dcp-core = { module = "org.eclipse.edc:identity-trust-core", version.ref = "edc" }
edc-dcp-issuersconfig = { module = "org.eclipse.edc:identity-trust-issuers-configuration", version.ref = "edc" }
edc-dcp-service = { module = "org.eclipse.edc:identity-trust-service", version.ref = "edc" }
edc-dcp-transform = { module = "org.eclipse.edc:identity-trust-transform", version.ref = "edc" }
edc-dcp-sts-client = { module = "org.eclipse.edc:identity-trust-sts-remote-client", version.ref = "edc" }

edc-did-core = { module = "org.eclipse.edc:identity-did-core", version.ref = "edc" }
edc-did-web = { module = "org.eclipse.edc:identity-did-web", version.ref = "edc" }
edc-oauth2-client = { module = "org.eclipse.edc:oauth2-client", version.ref = "edc" }
edc-sql-bootstrapper = { module = "org.eclipse.edc:sql-bootstrapper", version.ref = "edc" }
edc-sql-core = { module = "org.eclipse.edc:sql-core", version.ref = "edc" }
edc-sql-pool = { module = "org.eclipse.edc:sql-pool-apache-commons", version.ref = "edc" }
edc-sql-transactionlocal = { module = "org.eclipse.edc:transaction-local", version.ref = "edc" }
edc-sql-test-fixtures = { module = "org.eclipse.edc:sql-test-fixtures", version.ref = "edc" }
edc-junit = { module = "org.eclipse.edc:junit", version.ref = "edc" }
edc-spi-catalog = { module = "org.eclipse.edc:catalog-spi", version.ref = "edc" }
edc-spi-contract = { module = "org.eclipse.edc:contract-spi", version.ref = "edc" }
edc-spi-core = { module = "org.eclipse.edc:core-spi", version.ref = "edc" }
edc-spi-web = { module = "org.eclipse.edc:web-spi", version.ref = "edc" }
edc-spi-dsp = { module = "org.eclipse.edc:dsp-spi", version.ref = "edc" }
edc-spi-dsp08 = { module = "org.eclipse.edc:dsp-spi-08", version.ref = "edc" }
edc-spi-dsp-http = { module = "org.eclipse.edc:dsp-http-spi", version.ref = "edc" }
edc-spi-jsonld = { module = "org.eclipse.edc:json-ld-spi", version.ref = "edc" }
edc-spi-transform = { module = "org.eclipse.edc:transform-spi", version.ref = "edc" }
edc-spi-dataplane-selector = { module = "org.eclipse.edc:data-plane-selector-spi", version.ref = "edc" }
edc-spi-transaction-datasource = { module = "org.eclipse.edc:transaction-datasource-spi", version.ref = "edc" }
restAssured = { module = "io.rest-assured:rest-assured", version.ref = "restAssured" }

# EDC libs
edc-lib-api = { module = "org.eclipse.edc:api-lib", version.ref = "edc" }
edc-lib-boot = { module = "org.eclipse.edc:boot-lib", version.ref = "edc" }
edc-lib-jerseyproviders = { module = "org.eclipse.edc:jersey-providers-lib", version.ref = "edc" }
edc-lib-json = { module = "org.eclipse.edc:json-lib", version.ref = "edc" }
edc-lib-json-ld = { module = "org.eclipse.edc:json-ld-lib", version.ref = "edc" }
edc-lib-providers-jersey = { module = "org.eclipse.edc:jersey-providers-lib", version.ref = "edc" }
edc-lib-query = { module = "org.eclipse.edc:query-lib", version.ref = "edc" }
edc-lib-sql = { module = "org.eclipse.edc:sql-lib", version.ref = "edc" }
edc-lib-store = { module = "org.eclipse.edc:store-lib", version.ref = "edc" }
edc-lib-transform = { module = "org.eclipse.edc:transform-lib", version.ref = "edc" }
edc-lib-util = { module = "org.eclipse.edc:util-lib", version.ref = "edc" }

# protocol modules
edc-dsp-api-configuration = { module = "org.eclipse.edc:dsp-http-api-configuration-2025", version.ref = "edc" }
edc-dsp-all = { module = "org.eclipse.edc:dsp", version.ref = "edc" }
edc-dsp-transform-catalog-lib = { module = "org.eclipse.edc:dsp-catalog-transform-lib", version.ref = "edc" }
edc-dsp-transform-catalog2025 = { module = "org.eclipse.edc:dsp-catalog-transform-2025", version.ref = "edc" }
edc-controlplane-transform = { module = "org.eclipse.edc:control-plane-transform", version.ref = "edc" }

# third-party deps
jackson-jsr310 = { module = "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", version.ref = "jackson" }
postgres = { module = "org.postgresql:postgresql", version.ref = "postgres" }
testcontainers-junit = { module = "org.testcontainers:junit-jupiter", version.ref = "testcontainers" }

[bundles]
edc-dpf = ["edc-dpf-selector-spi", "edc-dpf-selector-core"]
dcp = ["edc-dcp-core", "edc-dcp-issuersconfig", "edc-dcp-service", "edc-dcp-sts-client", "edc-dcp-transform",
    "edc-did-core", "edc-did-web", "edc-oauth2-client"]

[plugins]
edc-build = { id = "org.eclipse.edc.edc-build", version = "0.0.2" }
shadow = { id = "com.gradleup.shadow", version = "8.3.8" }

