version: 2
updates:
  # maintain dependencies for GitHub actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly" # default = monday
    open-pull-requests-limit: 5
    labels:
      - "dependencies"
      - "github_actions"

  # maintain dependencies for Gradle
  - package-ecosystem: "gradle" # checks build.gradle(.kts) and settings.gradle(.kts)
    directory: "/"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 5
    labels:
      - "dependencies"
      - "java"
    ignore:
      - dependency-name: "org.eclipse.edc:*"

  - package-ecosystem: "docker"
    target-branch: main
    directory: launcher/catalog-dcp
    labels:
      - "dependabot"
      - "docker"
    schedule:
      interval: "weekly"

  - package-ecosystem: "docker"
    target-branch: main
    directory: launcher/catalog-mocked
    labels:
      - "dependabot"
      - "docker"
    schedule:
      interval: "weekly"
