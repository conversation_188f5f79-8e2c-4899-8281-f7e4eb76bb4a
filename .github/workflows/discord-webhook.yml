name: 'Discord Webhook'
on:
  issues:
    types: [ opened ]
  pull_request_target:
    types: [ opened, reopened ]
  discussion:
    types: [ created ]

jobs:
  message:
    runs-on: ubuntu-latest
    steps:
      - name: New Discussion
        uses: tsickert/discord-webhook@v6.0.0
        if: ${{ (github.event_name == 'discussion') }}
        with:
          webhook-url: ${{ secrets.DISCORD_GITHUB_WEBHOOK }}
          avatar-url: https://avatars.githubusercontent.com/u/9919?s=200&v=4
          embed-author-name: ${{ github.event.sender.login }}
          embed-author-url: ${{ github.event.sender.html_url }}
          embed-author-icon-url: ${{ github.event.sender.avatar_url }}
          embed-title: ${{ github.event.discussion.title }}
          embed-url: ${{ github.event.discussion.html_url }}
          embed-description: A **discussion** has been created in ${{ github.repository }}.

      - name: New Issue
        uses: tsickert/discord-webhook@v6.0.0
        if: ${{ (github.event_name == 'issues') }}
        with:
          webhook-url: ${{ secrets.DISCORD_GITHUB_WEBHOOK }}
          avatar-url: https://avatars.githubusercontent.com/u/9919?s=200&v=4
          embed-author-name: ${{ github.event.sender.login }}
          embed-author-url: ${{ github.event.sender.html_url }}
          embed-author-icon-url: ${{ github.event.sender.avatar_url }}
          embed-title: ${{ github.event.issue.title }}
          embed-url: ${{ github.event.issue.html_url }}
          embed-description: An **issue** has been opened in ${{ github.repository }}.

      - name: New Pull Request
        uses: tsickert/discord-webhook@v6.0.0
        if: ${{ (github.event_name == 'pull_request_target') }}
        with:
          webhook-url: ${{ secrets.DISCORD_GITHUB_WEBHOOK }}
          avatar-url: https://avatars.githubusercontent.com/u/9919?s=200&v=4
          embed-author-name: ${{ github.event.sender.login }}
          embed-author-url: ${{ github.event.sender.html_url }}
          embed-author-icon-url: ${{ github.event.sender.avatar_url }}
          embed-title: ${{ github.event.pull_request.title }}
          embed-url: ${{ github.event.pull_request.html_url }}
          embed-description: A **pull request** has been opened in ${{ github.repository }}.
