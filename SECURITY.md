# Security Policy

Eclipse Dataspace Connector (EDC) follows
the [Eclipse Vulnerability Reporting Policy](https://www.eclipse.org/security/policy.php). Vulnerabilities are tracked
by the Eclipse security team, in cooperation with the EDC project lead. Fixing vulnerabilities is taken care of by the
EDC project committers, with assistance and guidance of the security team.

## Supported Versions

Eclipse Dataspace Connector supports security updates for the following releases:

We will maintain the list of supported versions here starting from the first release

## Reporting a Vulnerability

We recommend that in case of suspected vulnerabilities you do not use the EDC public issue tracker, but instead contact
the Eclipse Security Team <NAME_EMAIL>.
