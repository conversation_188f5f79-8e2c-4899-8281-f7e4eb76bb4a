/*
 *  Copyright (c) 2023 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)
 *
 *  This program and the accompanying materials are made available under the
 *  terms of the Apache License, Version 2.0 which is available at
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Contributors:
 *       Bayerische Motoren Werke Aktiengesellschaft (BMW AG) - initial API and implementation
 *
 */

package org.eclipse.edc.catalog.spi;


import static org.eclipse.edc.spi.constants.CoreConstants.EDC_NAMESPACE;

public interface CatalogConstants {
    String PROPERTY_ORIGINATOR = EDC_NAMESPACE + "originator";
    String DATASPACE_PROTOCOL = "dataspace-protocol-http";
}
