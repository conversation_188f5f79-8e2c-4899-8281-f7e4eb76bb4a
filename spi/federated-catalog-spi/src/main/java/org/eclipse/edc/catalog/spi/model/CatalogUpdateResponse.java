/*
 *  Copyright (c) 2023 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)
 *
 *  This program and the accompanying materials are made available under the
 *  terms of the Apache License, Version 2.0 which is available at
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Contributors:
 *       Bayerische Motoren Werke Aktiengesellschaft (BMW AG) - initial API and implementation
 *
 */

package org.eclipse.edc.catalog.spi.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.eclipse.edc.connector.controlplane.catalog.spi.Catalog;
import org.eclipse.edc.crawler.spi.model.UpdateResponse;

public class CatalogUpdateResponse extends UpdateResponse {
    private final Catalog catalog;

    @JsonCreator
    public CatalogUpdateResponse(@JsonProperty("source") String source, @JsonProperty("catalog") Catalog catalog) {
        super(source);
        this.catalog = catalog;
    }

    public Catalog getCatalog() {
        return catalog;
    }
}
