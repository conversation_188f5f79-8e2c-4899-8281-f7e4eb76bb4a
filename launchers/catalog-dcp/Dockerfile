FROM eclipse-temurin:24.0.1_9-jre-alpine

# Optional JVM arguments, such as memory settings
ARG JVM_ARGS=""
RUN apk --no-cache add curl

WORKDIR /app

COPY ./build/libs/fc.jar /app

EXPOSE 8182

ENV WEB_HTTP_PORT="8181"
ENV WEB_HTTP_PATH="/api"

HEALTHCHECK --interval=5s --timeout=5s --retries=10 CMD curl --fail http://localhost:8181/api/check/health

# Use "exec" for graceful termination (SIGINT) to reach JVM.
# ARG can not be used in ENTRYPOINT so storing value in an ENV variable
ENV ENV_JVM_ARGS=$JVM_ARGS
ENV ENV_APPINSIGHTS_AGENT_VERSION=$APPINSIGHTS_AGENT_VERSION
ENTRYPOINT [ "sh", "-c", "exec java $ENV_JVM_ARGS -jar fc.jar"]
